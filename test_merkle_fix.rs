// Simple test to verify the async function signature fix
use std::path::PathBuf;
use serde_json::Value;

// Mock the dependencies for testing
struct MockError;
impl std::fmt::Display for MockError {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        write!(f, "Mock error")
    }
}
impl std::fmt::Debug for MockError {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        write!(f, "Mock error")
    }
}
impl std::error::Error for MockError {}

// This is the corrected function signature - should compile without the Pin<Box<Future>> error
async fn build_hash_tree(root_dir: &PathBuf, base_patterns: &[String], sleep_time: usize) -> Result<Value, Box<dyn std::error::Error>> {
    // Mock implementation
    Ok(serde_json::json!({"test": "value"}))
}

// Test function that uses the corrected async function
async fn test_build_merkle_tree(root_dir: &String, sleep_time: usize) -> Result<Value, Box<dyn std::error::Error>> {
    let target_path = PathBuf::from(root_dir);
    let base_patterns = vec!["*.tmp".to_string()];
    
    // This should now work without the Pin<Box<Future>> error
    let tree = build_hash_tree(&target_path, &base_patterns, sleep_time).await?;
    Ok(tree)
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let result = test_build_merkle_tree(&"/tmp".to_string(), 10).await?;
    println!("Success: {:?}", result);
    Ok(())
}
