use crate::dialogue::codefuse_index_repository::{CHUNK_FIELDS, FILE_CLIET, FILE_FIELDS, METHOD_CLIENT, METHOD_FIELDS};
use crate::dialogue::data_struct::{QueryChatItemResult, Query<PERSON>hatRelatedData, QueryResultItem};
use crate::dialogue::repo_index_operator::CACHE_KEY;
use crate::service::code_scan::skip_dir;
use crate::utils::parallel_file_scan::{execute_scan_for_index_repository};
use crate::utils::file_encoding::read_file_smart_sync;
use agent_common_service::model::chat_model::ChatRelatedRequestBean;
use agent_db::config::runtime_config::{AgentConfig, AGENT_CONFIG};
use agent_db::dal::kv_client::KV_CLIENT;
use agent_db::dal::remote_client::IndexTypeEnum::{CHUNK_CONTENT, CHUNK_VECTOR, CLASS_ANNOTATION, FILE_CONTENT, METHOD_ANNOTATION, METHOD_CONTENT};
use agent_db::dal::remote_client::ResponseResult::SUCCESS;
use agent_db::dal::remote_client::{vec_to_hashset, IndexTypeEnum, TraceInfo, ResponseResult, SUBMIT_EVENT_TRACK};
use agent_db::domain::code_chat_domain::{ChatRelatedCodeModel, CodefuseChunk, CodefuseFile, CodefuseMethod};
use agent_db::remote::http_client::{post_request, post_request_sync};
use agent_db::remote::rpc_model::build_success_response;
use agent_db::tools::common_tools::{collect_device_info, expand_user_home, LINE_ENDING, V_C_PREFIX,  V_M_PREFIX};
use blake3::Hasher;
use ignore::{DirEntry, WalkBuilder};
use log::{error, info};
use regex::Regex;
use std::backtrace::Backtrace;
use std::collections::{HashMap, HashSet};
use std::error::Error;
use std::fs::File;
use std::io::Write;
use std::panic::PanicHookInfo;
use std::path::{Path, MAIN_SEPARATOR};
use std::sync::{Arc, Once};
use std::time::{SystemTime, UNIX_EPOCH};
use std::{env, fs, panic, thread};
use tantivy::{Score, TantivyDocument};
use tokio::runtime::Runtime;
use tokio::sync::RwLock;
use tokio::task;

///判断文件后缀是否是java
pub fn is_java_file(path: &Path) -> Option<bool> {
    match path.extension() {
        Some(ext) => {
            if let Some(ext_str) = ext.to_str() {
                Some(ext_str == "java")
            } else {
                Some(false)
            }
        }
        None => {
            Some(false)
        }
    }
}


pub async fn return_none() -> Result<Vec<(Score, TantivyDocument)>, Box<dyn Error>> {
    Ok(vec![])
}

pub fn doc_to_chunk(score: Score, tantivy_doc: TantivyDocument) -> QueryResultItem<CodefuseChunk> {
    let c_obj = CHUNK_FIELDS.convert_to_chunk(tantivy_doc);
    let query_chunk_item = QueryResultItem { score: score, data: c_obj };
    query_chunk_item
}

pub fn doc_to_method(score: Score, tantivy_doc: TantivyDocument) -> QueryResultItem<CodefuseMethod>{
    let m_obj = METHOD_FIELDS.convert_to_method(tantivy_doc);
    let query_method_item = QueryResultItem { score: score, data: m_obj };
    query_method_item
}

pub fn doc_to_file(score: Score, tantivy_doc: TantivyDocument) -> QueryResultItem<CodefuseFile>{
    let m_obj = FILE_FIELDS.convert_to_file(tantivy_doc);
    let query_method_item = QueryResultItem { score: score, data: m_obj };
    query_method_item
}

///id在存储查询的时候非常重要，提出来单独调用
///file存文本索引库的id
pub fn create_file_id(s: String) -> String {
    s
}
///method存文本索引库的id
pub fn create_method_id(path: String, class_join_name: String, method_signature: String) -> String {
    let method_id = format!("{}{}#{}{}{}", V_M_PREFIX, path, class_join_name, "#", method_signature).trim().to_string();
    method_id
}
///根据文件路径获取kv库里method向量的前缀, 文件级别的方法vector前缀
pub fn get_file_method_vector_prefix(file_path: String) -> String {
    let method_id_prefix = format!("{}{}#", V_M_PREFIX, file_path).trim().to_string();
    method_id_prefix
}
///仓库级别的method向量前缀
pub fn get_project_method_vector_prefix(project_url: String) -> String {
    let method_id_prefix = format!("{}{}", V_M_PREFIX, project_url).trim().to_string();
    method_id_prefix
}
///chunk存文本索引库的id
pub fn create_chunk_id(file_path: String, chunk_index: usize) -> String {
    let chunk_id = format!("{}{}#{}", V_C_PREFIX, file_path, chunk_index).trim().to_string();
    chunk_id
}
///根据文件路径获取kv库里chunk向量的前缀，文件级别的chunk vector前缀
pub fn get_file_chunk_vector_prefix(file_path: &str) -> String {
    let chunk_id_prefix = format!("{}{}#", V_C_PREFIX, file_path).trim().to_string();
    chunk_id_prefix
}
///仓库级别的chunk向量前缀
pub fn get_project_chunk_vector_prefix(project_url: String) -> String {
    let method_id_prefix = format!("{}{}", V_C_PREFIX, project_url).trim().to_string();
    method_id_prefix
}

///从file summary vector_id转回file_id
pub fn create_file_id_from_fs_vector_id(summary_vector_id: String) -> String {
    let parts: Vec<&str> = summary_vector_id.splitn(2, '_').collect();
    if parts.len() > 1 {
        let file_id = parts[1];
        return file_id.to_string();
    }
    "".to_string()
}


///从chunk summary vector_id转回chunk_id
pub fn create_chunk_id_from_cs_vector_id(summary_vector_id: String) -> String {
    let parts: Vec<&str> = summary_vector_id.splitn(2, '_').collect();
    if parts.len() > 1 {
        let chunk_suffix = parts[1];
        let chunk_id = format!("{}{}", V_C_PREFIX, chunk_suffix);
        return chunk_id;
    }
    "".to_string()
}


pub async fn init_panic_hook(){
    info!("init_panic_hook!");
    // 设置panic hook
    let panic_hook = move |info: &panic::PanicHookInfo| {
        capture_stack_info(info);
        error!("Panic occurred, exit!");
        std::process::exit(1);
    };
    panic::set_hook(Box::new(panic_hook));
}

fn capture_stack_info(panic_info: &PanicHookInfo){
    info!("stack_info_capture...threadname : {}", thread::current().name().unwrap_or("unknown"));
    // 捕获背后的堆栈跟踪
    // let backtrace = Backtrace::capture();
    let backtrace = Backtrace::force_capture();
    // 获取 panic 信息
    let payload = if let Some(s) = panic_info.payload().downcast_ref::<&str>() {
        *s
    } else {
        "Unknown panic payload"
    };

    let location = if let Some(location) = panic_info.location() {
        format!("{}:{}", location.file(), location.line())
    } else {
        "Unknown location".to_string()
    };

    // 构建错误信息
    let error_message = format!("Panic occurred: {} at {}", payload, location);
    let panic_msg = format!("{}\n{}", error_message, backtrace);

    info!("panic_msg: {}", panic_msg);

    let mut panic_msg_info = TraceInfo::default();
    panic_msg_info.data = panic_msg;
    panic_msg_info.ideVersion = "".to_string();
    panic_msg_info.pluginVersion = "".to_string();
    panic_msg_info.userToken = "".to_string();
    panic_msg_info.deviceInfo = collect_device_info();
    panic_msg_info.occurTime = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis().to_string();

    write_to_file(&serde_json::to_string(&panic_msg_info).unwrap());
}
pub(crate) fn query_method_list(file_url: &String, start_line: u64, end_line: u64) -> (Vec<CodefuseMethod>, Vec<CodefuseMethod>) {
    let mut query_param = HashMap::new();
    query_param.insert("file_url".to_string(), file_url.clone());
    //最多取100个函数,出现有不够的问题，改成300，应该够用了
    let method_vec_result = METHOD_CLIENT.query(&query_param, 300);
    //找到命中行号的函数
    let mut method_detail_vec = vec![];
    //找到未命中行号的函数
    let mut method_info_vec = vec![];
    match method_vec_result {
        Ok(method_result) => {
            for codefuse_method_doc in method_result {
                let method_info = doc_to_method(codefuse_method_doc.0, codefuse_method_doc.1).data;
                // if ((start_line >= method_info.start_line && end_line <= method_info.end_line) ||
                //     (start_line <= method_info.start_line && end_line >= method_info.start_line) ||
                //     (start_line <= method_info.end_line && end_line >= method_info.end_line)) && start_line != end_line {
                //     method_detail_vec.push(method_info);
                // } else {
                //     method_info_vec.push(method_info);
                // }
                if method_info.start_line > end_line || method_info.end_line < start_line {
                    //没有交集
                    method_info_vec.push(method_info);
                }else {
                    //有交集
                    method_detail_vec.push(method_info);
                }
            }
        }
        Err(e) => {
            error!("query_method_list_from_chunk error:{:?}",e);
        }
    }
    (method_detail_vec, method_info_vec)
}

pub(crate) const CLASS_REGEX: &str = r"(?m)^\s*(?:@\w+\s+)?(?:public|protected|private|abstract|final|static|\s)*\s*(class|enum|interface|@interface)\s+(\w+)";


pub(crate) fn get_simplify_method_vec(method_info_vec: &Vec<CodefuseMethod>) -> Option<Vec<CodefuseMethod>> {
    let mut method_vec = vec![];
    for method in method_info_vec {
        let id = &method.id;
        // 查找 '#' 的位置
        if let Some(pos) = id.find('#') {
            // 获取 '#' 后面的子字符串
            let method_declaration = &id[pos + 1..];
            let mut new_content = String::new();
            if method.annotate.len() > 0 {
                new_content.push_str(&method.annotate);
                new_content.push_str(LINE_ENDING);
            }
            new_content.push_str(method_declaration);
            new_content.push_str("{");
            new_content.push_str(LINE_ENDING);
            new_content.push_str("}");
            new_content.push_str(LINE_ENDING);
            let new_method = CodefuseMethod {
                id: method.id.clone(),
                file_url: method.file_url.clone(),
                content: new_content,
                start_line: 0,
                end_line: 0,
                summary: "".to_string(),
                annotate: "".to_string(),
                project_url: "".to_string(),
                branch: "".to_string(),
                hash: "".to_string(),
                feature: "".to_string(),
                summary_keyword: Vec::new(),
                has_summary: 0,
                has_content_vector: 0,
                has_summary_vector: 0,
            };
            method_vec.push(new_method);
        }
    }
    if method_vec.len() == 0 {
        None
    } else {
        Some(method_vec)
    }
}

fn query_class_construct(file_url: &String, detail_method_vec: &Vec<CodefuseMethod>, method_info_vec: &Vec<CodefuseMethod>) -> Option<CodefuseFile> {
    let mut query_param = HashMap::new();
    query_param.insert("id".to_string(), file_url.clone());
    let file_vec_result = FILE_CLIET.query(&query_param, 1);

    match file_vec_result {
        Ok(file_data_vec) => {
            if file_data_vec.len() == 1 {
                for file_data in file_data_vec {
                    let file_info = doc_to_file(file_data.0, file_data.1).data;
                    if !file_info.id.ends_with(".java") {
                        continue;
                    }
                    //只有命中class结构声明才去组装class结构
                    if let Some(captures) = Regex::new(CLASS_REGEX).unwrap().captures(&file_info.content) {
                        let mut result = String::new();
                        if file_info.annotate.len() > 0 {
                            result.push_str(&file_info.annotate);
                            result.push_str(LINE_ENDING)
                        }
                        if let Some(class_type) = captures.get(2) {
                            let class_declaration = class_type.as_str();
                            result.push_str(&class_declaration.to_string());
                            result.push_str(LINE_ENDING);
                            //1:detail_method_vec和method_info_vec都是0，说明检索到的chunk没命中任何函数，那么尝试重新查询所有函数

                            if detail_method_vec.len() == 0 && method_info_vec.len() == 0 {
                                let method_vec = query_method_list(&file_info.id, 0, 0);
                                let simple_method_vec = get_simplify_method_vec(&method_vec.1);
                                match simple_method_vec {
                                    Some(method_vec) => {
                                        for method in method_vec {
                                            result.push_str(&method.content);
                                            result.push_str(LINE_ENDING);
                                        }
                                    }
                                    None => {
                                        info!("query_class_construct,simple_method_vec is None.file_url:{:?}",file_url);
                                    }
                                }
                            } else {
                                for method_info in detail_method_vec {
                                    result.push_str(&method_info.content);
                                    result.push_str(LINE_ENDING);
                                }
                                let simple_method_vec = get_simplify_method_vec(&method_info_vec);
                                match simple_method_vec {
                                    Some(method_vec) => {
                                        for method in method_vec {
                                            result.push_str(&method.content);
                                            result.push_str(LINE_ENDING);
                                        }
                                    }
                                    None => {
                                        info!("query_class_construct,simple_method_vec is None.file_url:{:?}",file_url);
                                    }
                                }
                            }
                            result.push_str("}");

                            let result = CodefuseFile {
                                id: file_url.clone(),
                                content: result.clone(),
                                summary: "".to_string(),
                                annotate: "".to_string(),
                                project_url: "".to_string(),
                                branch: "".to_string(),
                                hash: "".to_string(),
                                feature: "".to_string(),
                                summary_keyword: Vec::new(),
                                has_summary: 0,
                                has_summary_vector: 0
                            };
                            return Some(result);
                        }
                    }
                }
            } else {
                error!("query_class_construct,size not 1:{:?}", file_data_vec)
            }
        }
        Err(e) => {
            error!("query_class_construct error:{:?}",e);
        }
    }
    None
}

pub fn build_extra_data(query_chat_related_data: &mut QueryChatRelatedData) {
    if let Some(chunk_content_data_value) = &mut query_chat_related_data.chunk_content_data {
        addition_for_codefusechunk_data(chunk_content_data_value);
    }
    if let Some(chunk_content_data_value) = &mut query_chat_related_data.chunk_vector_data {
        addition_for_codefusechunk_data(chunk_content_data_value);
    }

    if let Some(method_content_data_value) = &mut query_chat_related_data.method_content_data {
        addition_for_codefusemethod_data(method_content_data_value);
    }
}

pub fn convert_from_codefusemethod_to_model(codefuse_method: CodefuseMethod) -> ChatRelatedCodeModel{
    let relative_path = get_relative_path(&codefuse_method.project_url, &codefuse_method.file_url);
    let chat_related_code_model = ChatRelatedCodeModel{
        relativePath: relative_path,
        snippet: codefuse_method.content,
        startLine: codefuse_method.start_line as usize,
        endLine: codefuse_method.end_line as usize,
        source: None,
        title: None,
    };
    chat_related_code_model
}

///用chunk的数据填充，主要是用在没有codefusemethod数据的时候
pub fn convert_from_codefusechunk_to_model(codefuse_chunk: CodefuseChunk) -> ChatRelatedCodeModel{
    let relative_path = get_relative_path(&codefuse_chunk.project_url, &codefuse_chunk.file_url);
    let chat_related_code_model = ChatRelatedCodeModel{
        relativePath: relative_path,
        snippet: codefuse_chunk.content,
        startLine: codefuse_chunk.start_line as usize,
        endLine: codefuse_chunk.end_line as usize,
        source: None,
        title: None,
    };
    chat_related_code_model
}

///补充查询类型是codefusemethod的数据
pub fn addition_for_codefusemethod_data(method_content_data_value: &mut Vec<QueryChatItemResult<CodefuseMethod>>){
    for method_content_result in method_content_data_value.iter_mut() {
        let item = &method_content_result.search_result.data;
        let method_info = query_method_list(&item.file_url, item.start_line, item.end_line);
        let detail_method_vec = &method_info.0;
        let simple_method_vec = &method_info.1;
        method_content_result.extend_file_result = query_class_construct(&item.file_url, detail_method_vec, simple_method_vec);
    }
}
///补充查询类型是codefusechunk的数据
pub fn addition_for_codefusechunk_data(chunk_content_data_value: &mut Vec<QueryChatItemResult<CodefuseChunk>>){
    for chunk_content_result in chunk_content_data_value.iter_mut() {
        let item = &chunk_content_result.search_result.data;
        let method_info = query_method_list(&item.file_url, item.start_line, item.end_line);
        let detail_method_vec = &method_info.0;
        let simple_method_vec = &method_info.1;
        if detail_method_vec.len() > 0 {
            chunk_content_result.extend_method_result = Some(detail_method_vec.clone());
        }
        chunk_content_result.extend_file_result = query_class_construct(&item.file_url, detail_method_vec, simple_method_vec);
    }
}

///计算file, method, chunk的hash值，判断内容是否发生变化
pub fn calculate_content_hash(txt: &str) -> String {
    let mut hasher = Hasher::new();
    hasher.update(txt.as_bytes());
    let hash = hasher.finalize();
    hash.to_hex().to_string()
}



//是否需要加速, true意味着可以最大并发，false意味着不能高并发
pub fn accelerate_by_time(mill_sleep_time: usize) -> bool {
    if mill_sleep_time == 0 {
        true
    }else {
        false
    }
}


//自动判断是否要sleep, 阻塞sleep
pub fn autonomous_sleep_with_bolck(milli_sleep_time: usize) {
    if !accelerate_by_time(milli_sleep_time) {
        thread::sleep(std::time::Duration::from_millis(milli_sleep_time as u64));
    }
}
//自动判断是否要sleep, 非阻塞sleep
pub async fn autonomous_sleep_no_bolck(milli_sleep_time: usize) {
    if !accelerate_by_time(milli_sleep_time) {
        tokio::time::sleep(tokio::time::Duration::from_millis(milli_sleep_time as u64)).await;
    }
}
//获取相对路径
pub fn get_relative_path(prefix: &String, absolute_path: &String) -> String{
    let with_spelerator = format!("{}{}", prefix,  MAIN_SEPARATOR);
    let rst = absolute_path.strip_prefix(&with_spelerator);
    if let Some(relative_path) = rst {
        relative_path.to_string()
    }else {
        absolute_path.to_string()
    }
}
///仿rayon的方式，获取逻辑核数
pub fn get_logical_core_num() -> usize {
    num_cpus::get()
}

///获取同时并发的数量
pub fn get_parallel_num(mill_sleep_time: usize) -> usize {
    if accelerate_by_time(mill_sleep_time) {
        //加速场景，就返回逻辑核数
        get_logical_core_num()
    }else {
        //非加速场景，就返回1
        AGENT_CONFIG.index_async_thread_num as usize
    }
}



///保存panic信息到文件
fn write_to_file(content: &String) {
    // 创建panic info存储路径，如果不存在则创建
    let panic_cache_url = expand_user_home(&AGENT_CONFIG.base_data_url).unwrap().join("panic");
    if !panic_cache_url.exists() {
        let _ = std::fs::create_dir_all(&panic_cache_url);
    }
    let current_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    let file_name = current_time.to_string() +".log";
    let file_path = panic_cache_url.join(Path::new(&file_name));
    match File::create(&file_path) {
        Ok(mut file) => {
            info!("create file success");
            file.write_all(content.as_bytes()).unwrap();
        }
        Err(e) => {info!("create file error, {:?}", e)}
    }
}


///删除数据,万一有数据不兼容的情况，需要清除数据
fn delete_chat_data() {
    //删除索引数据
    let old_data_url = expand_user_home(&AGENT_CONFIG.base_data_url).unwrap().join("chat");
    if old_data_url.exists() {
        let result = fs::remove_dir_all(&old_data_url); // 删除老版本数据
        if let Err(e) = result {
            error!("del dir error {}", e);
        }
    }
    //删除工程的缓存数据
    KV_CLIENT.delete(&CACHE_KEY.to_string());
}

//文件的上传统一给插件来做， 这个回传的方法暂时没啥用处
// 上传本地的panic info, 上传后需要删除本地的文件,
pub async fn handle_panic_info() {
    info!("init_panic_hook!");
    let panic_cache_url = expand_user_home(&AGENT_CONFIG.base_data_url).unwrap().join("panic");
    if !panic_cache_url.exists() {
        return;
    }

    let panic_cache_rst = fs::read_dir(panic_cache_url);
    match panic_cache_rst {
        Ok(panic_cache) => {
            for entry in panic_cache {
                let entry = entry.unwrap();
                let entry_path = entry.path();
                if entry_path.is_file() {
                    info!("entry_path {}", entry_path.display());
                    // 智能读取文件内容，支持多种编码（UTF-8, GBK, GB2312, GB18030等）
                    let read_content_rst = read_file_smart_sync(&entry_path.to_string_lossy());
                    if let Ok(content) = read_content_rst {
                        let panicmsg_rst: Result<TraceInfo, serde_json::Error> = serde_json::from_str(&content);
                        match panicmsg_rst {
                            Ok(panicmsg) => {
                                let upload_rst = upload_panic_info(panicmsg).await;
                                if upload_rst {
                                    let remove_file_rst = fs::remove_file(entry_path);
                                    match remove_file_rst {
                                        Ok(_) => {info!("remove file success");}
                                        Err(e) => {info!("remove file error {}", e);}
                                    }
                                }
                            }
                            Err(e) => {info!("Error parsing JSON: {:?}", e)}
                        }
                    }
                }
            }
        }
        Err(e) => {info!("panic_cache_url error {:?}", e);}
    }
}
pub async fn upload_panic_info (panic_msg_info : TraceInfo) -> bool {
    let data = build_success_response(panic_msg_info);
    println!("start summit panic to server ...");
    let submit_track_res = post_request::<ResponseResult, _>(SUBMIT_EVENT_TRACK, &data, 5 * 1000, "submit_panic_info_error").await;
    println!("submit_track_res : {:?}", submit_track_res);
    if let Some(submit_res) = submit_track_res {
        match submit_res.data {
            None => {
                return false;
            }
            Some(res) => {
                match res {
                    SUCCESS => {return true;}
                    _ => {return false}
                }
            }
        }
    }
    false
}
