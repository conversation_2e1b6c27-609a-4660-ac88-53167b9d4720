pub mod function;

pub mod service;
pub mod ast;
pub mod utils;
pub mod dialogue;
pub mod deepsearchV2;
pub mod constants;
pub mod ast_chunk;
pub mod remote;

#[cfg(test)]
mod tests {
    use std::path::Path;

    use ignore::{DirE<PERSON>ry, WalkBuilder};

    #[test]
    fn test1() {
        let base = Path::new("/Users/<USER>/RustroverProjects/codefuse_local_agent");


        // for result in Walk::new(base) {
        for result in WalkBuilder::new(base).filter_entry(|e| !skip_file(e)).build() {
            // Each item yielded by the iterator is either a directory entry or an
            // error, so either print the path or the error.
            match result {
                Ok(entry) => println!("{}", entry.path().display()),
                Err(err) => println!("ERROR: {}", err),
            }
        }
    }

    fn skip_file(entry: &DirEntry) -> bool {
        let file_name = entry.file_name().to_string_lossy();
        if file_name.ends_with("agent_db") {
            return true;
        }
        false
    }
}

