use serde::{Deserialize, Serialize};

///rpc返回值base结构
#[derive(Debug, Serialize, Deserialize,Clone)]
pub struct BaseResponse<T> {
    ///错误码
    pub errorCode: u8,
    ///错误信息
    pub errorMsg: Option<String>,
    ///rpc结果
    pub data: Option<T>,
    ///分页总数
    pub totalCount: Option<usize>,
}

impl<T> Default for BaseResponse<T> {
    fn default() -> Self {
        BaseResponse {
            errorCode: 0,
            errorMsg: None,
            data: None,
            totalCount: None,
        }
    }
}

pub fn build_success_response_no_result() -> BaseResponse<()> {
    BaseResponse {
        errorCode: 0,
        errorMsg: None,
        data: None,
        totalCount: None,
    }
}

pub fn build_success_response<T>(data: T) -> BaseResponse<T> {
    BaseResponse {
        errorCode: 0,
        errorMsg: None,
        data: Some(data),
        totalCount: None,
    }
}

pub fn build_error_response<T>(error_code: u8, error_msg: String) -> BaseResponse<T> {
    BaseResponse {
        errorCode: error_code,
        errorMsg: Some(error_msg),
        data: None,
        totalCount: None,
    }
}

#[derive(Debug, Serialize, Deserialize,Clone)]
pub struct CodeInsightBaseResponse<T> {
    //请求是否成功
    pub success: bool,
    //请求响应码
    pub code: String,
    //请求失败时，提供错误信息
    pub message: Option<String>,
    ///rpc结果
    pub data: Option<T>,
}
