use log::info;
use sha1::{Digest, Sha1};
use crate::remote::merkle_tree_diff::DiffResult;
use crate::remote::remote_syn_data::{DiffFileData, RemoteIndexConfig};
use crate::remote::remote_syn_net::request_merkle_tree_config;

//没有content
pub fn get_diff_file_vec (diff_result: &DiffResult) -> Vec<DiffFileData> {
    let mut diff_file_vec = Vec::new();

    for add_item in diff_result.added_files.clone() {
        let diff_add_file = DiffFileData {
            relativePath: add_item,
            hash: None,
            operation: "add".to_string(),
            content: "".to_string(),
        };
        diff_file_vec.push(diff_add_file);
    }
    for delete_item in diff_result.removed_files.clone() {
        let diff_remove_file = DiffFileData {
            relativePath: delete_item.clone(),
            hash: None,
            operation: "delete".to_string(),
            content: "".to_string(),
        };
        diff_file_vec.push(diff_remove_file);
    }
    for modify_item in diff_result.modified_files.clone() {
        let diff_remove_file = DiffFileData {
            relativePath: modify_item.clone(),
            hash: None,
            operation: "modify".to_string(),
            content: "".to_string(),
        };
        diff_file_vec.push(diff_remove_file);
    }
    diff_file_vec
}

//生成sessionId， 规则是userIdprojectUrl， 然后计算hash
pub fn generate_session_id(userId: &String, project_url: &String) -> String {
    let input = format!("{}{}", userId, project_url);
    hash_content(&input)
}
//计算hash
pub fn hash_content(input: &String) -> String {
    let mut hasher = Sha1::new();
    hasher.update(input.as_bytes());
    let hash_value = hex::encode(hasher.finalize());
    hash_value
}

pub async fn get_remote_index_config() -> Option<RemoteIndexConfig> {
    //默认值
    let mut merkle_tree_config_d = RemoteIndexConfig::default();
    let merkle_tree_config_opt = request_merkle_tree_config().await;
    info!("merkle_tree_config_opt {:?}", merkle_tree_config_opt);

    match merkle_tree_config_opt {
        None => {Some(merkle_tree_config_d)}
        Some(v) => {Some(v)}
    }
}