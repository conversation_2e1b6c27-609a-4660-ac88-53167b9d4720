use std::collections::HashMap;
use agent_db::remote::http_client::{post_request, post_request_with_header, post_request_without_body};
use agent_db::remote::rpc_model::{BaseResponse, CodeInsightBaseResponse};
use crate::remote::remote_syn_data::{ RemoteIndexConfig, RequestMerkleTreeData, RequestMerkleTreeResponseData, SearchFromRemoteRequestData, SearchFromRemoteResponse, UpdateMerkleTreeData, UpdateMerkleTreeResponseData, UploadDiffFileData, UploadDiffFileDataResponse};

//从caselike请求配置, 上线时需要上线， todo
pub const REQUEST_CONFIG_SERVER_URL: &str =  "https://caselike-pre.alipay.com/v1/agent/config/getCodebaseSearchConfig";
//查询merkle tree
pub const REQUEST_MERKLE_TREE_SERVER_URL: &str =  "https://codeinsightapi.alipay.com/api/v1/mcp/repos/merkle/tree/query";
//更新merkle tree
pub const UPDATE_MERKLE_TREE_SERVER_URL: &str =  "https://codeinsightapi.alipay.com/api/v1/mcp/repos/merkle/tree/update";
//上传变更文件
pub const UPLOAD_DIFF_FILE_SERVER_URL: &str =  "https://codeinsightapi.alipay.com/api/v1/mcp/repos/sync/upload";
//检索数据
pub const SEARCH_FROM_REMOTE_SERVER_URL: &str =  "https://codeinsightapi.alipay.com/api/v1/mcp/repos/query/search";

pub const AUTHORIZATION_TOKEN: &str = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWJqZWN0Ijp7InJvbGVzIjpbIk1DUF9TRVJWRVIiXSwidXNlciI6ImNvbmdwaW5nLmxjcEBhbnRncm91cC5jb20yMjEzMjMiLCJwbGF0Zm9ybSI6InNreWxpbmUtY29kZWJhc2UifSwidHlwZSI6ImFjY2VzcyIsImV4cCI6MjYyMDE5NTQ2NiwiaWF0IjoxNzU2MjgxODY2LCJqdGkiOiJiYWZkMTAzNy00MmQzLTQ2MjgtYTc3ZS1lMzg3ZWIyYzNmMTMifQ.XKYqpGFZ822Ti7FtqYWsCeen2VjqNHR4hYs_GcsiTCo";
pub const REQUEST_REMOTE_TIMEOUT: u64 = 3000;

//查询merkle tree
pub async fn request_remote_merkle_tree(request_merkle_tree_data: &RequestMerkleTreeData) -> Option<RequestMerkleTreeResponseData>{
    let mut headers = HashMap::new();
    headers.insert("Authorization", AUTHORIZATION_TOKEN);
    headers.insert("sofa-traceid", "");
    let request_merkle_tree_res_opt = post_request_with_header::<CodeInsightBaseResponse<RequestMerkleTreeResponseData>, _>(
        REQUEST_MERKLE_TREE_SERVER_URL,
        request_merkle_tree_data,
        headers,
        REQUEST_REMOTE_TIMEOUT,
        "request_merkle_tree fail").await;

    match request_merkle_tree_res_opt {
        None => {None}
        Some( request_merkle_tree_res) => {
            request_merkle_tree_res.data
        }
    }
}

//查询merkle tree
pub async fn request_update_merkle_tree(update_merkle_tree_data: &UpdateMerkleTreeData) -> Option<UpdateMerkleTreeResponseData>{
    let mut headers = HashMap::new();
    headers.insert("Authorization", AUTHORIZATION_TOKEN);
    headers.insert("sofa-traceid", "");
    let request_merkle_tree_res_opt = post_request_with_header::<CodeInsightBaseResponse<UpdateMerkleTreeResponseData>, _>(UPDATE_MERKLE_TREE_SERVER_URL,
                                                                                               update_merkle_tree_data,
                                                                                               headers,
                                                                                                                       REQUEST_REMOTE_TIMEOUT,
                                                                                               "update_merkle_tree fail").await;
    match request_merkle_tree_res_opt {
        None => {None}
        Some( request_merkle_tree_res) => {
            request_merkle_tree_res.data
        }
    }
}

//变更提交
pub async fn request_upload_diff_file(upload_diff_file: &UploadDiffFileData) -> Option<UploadDiffFileDataResponse>{
    let mut headers = HashMap::new();
    headers.insert("Authorization", AUTHORIZATION_TOKEN);
    headers.insert("sofa-traceid", "");
    let request_merkle_tree_res_opt = post_request_with_header::<CodeInsightBaseResponse<UploadDiffFileDataResponse>, _>(
        UPLOAD_DIFF_FILE_SERVER_URL,
        upload_diff_file,
        headers,
        REQUEST_REMOTE_TIMEOUT,
        "upload_diff_file fail").await;

    match request_merkle_tree_res_opt {
        None => {None}
        Some( request_merkle_tree_res) => {
            request_merkle_tree_res.data
        }
    }
}
//代码检索
pub async fn request_search_from_remote(search_from_remote: &SearchFromRemoteRequestData) -> Option<SearchFromRemoteResponse>{
    let mut headers = HashMap::new();
    headers.insert("Authorization", AUTHORIZATION_TOKEN);
    headers.insert("sofa-traceid", "");
    let request_merkle_tree_res_opt = post_request_with_header::<CodeInsightBaseResponse<SearchFromRemoteResponse>, _>(
        SEARCH_FROM_REMOTE_SERVER_URL,
        search_from_remote,
        headers,
        REQUEST_REMOTE_TIMEOUT,
        "search_from_remote fail").await;
    match request_merkle_tree_res_opt {
        None => {None}
        Some( request_merkle_tree_res) => {
            request_merkle_tree_res.data
        }
    }
}

//请求配置
pub async fn request_merkle_tree_config() -> Option<RemoteIndexConfig> {
    let request_config_res_opt = post_request_without_body::<RemoteIndexConfig>(REQUEST_CONFIG_SERVER_URL,
                    REQUEST_REMOTE_TIMEOUT,
                    "request_config fail"
    ).await;
    match request_config_res_opt {
        None => {None}
        Some( request_config_res) => {
            request_config_res.data
        }
    }
}