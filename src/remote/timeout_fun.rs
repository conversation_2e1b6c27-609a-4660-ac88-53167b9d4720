use tokio::time::{timeout, Duration};
use std::error::Error;
use std::fmt;

// 定义一个自定义错误类型，用于表示任务超时
#[derive(Debug)]
struct TaskTimeoutError;

impl fmt::Display for TaskTimeoutError {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "Task timed out")
    }
}

impl Error for TaskTimeoutError {}

/// 异步执行一个任务，并设置超时时间。
///
/// # 参数
/// - `task_future`: 一个异步任务，返回一个 `Result<T, E>`。
/// - `timeout_duration`: 任务的超时时间。
///
/// # 返回
/// - `Ok(T)`: 如果任务在超时时间内完成并成功返回结果。
/// - `Err(TaskTimeoutError)`: 如果任务超时。
/// - `Err(E)`: 如果任务在超时时间内完成但返回了一个错误。
pub async fn run_task_with_timeout<T, E>(
    // task_future: F,
    task_future: impl std::future::Future<Output = Result<T, E>> + Send + 'static, // 使用 impl Future
    timeout_duration: Duration,
) -> Result<T, Box<dyn Error + Send + Sync>>
where
    T: Send + 'static,
    E: Error + Send + Sync + 'static,
{
    // 使用 tokio::time::timeout 包装任务
    let result = timeout(timeout_duration, task_future).await;

    match result {
        Ok(Ok(value)) => Ok(value), // 任务在超时前完成，并成功返回结果
        Ok(Err(task_err)) => Err(task_err.into()), // 任务在超时前完成，但返回了错误
        Err(_) => Err(Box::new(TaskTimeoutError)), // 任务超时
    }
}